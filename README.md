python msgpack2parquet.py \
  --src /Users/<USER>/Projects/scalpel_new/daily_combined_data_coinapi \
  --dst /Users/<USER>/Projects/scalpel_new/parquet_raw \
  --workers 16 \
  --start-date 2025-07-07 --end-date 2025-07-08

# PRECOMPUTE INDICATORS
python precompute_indicators.py \
  --root-dir   parquet_copy \
  --dst-dir    parquet_processed \
  --symbol     XRPUSDC \
  --cfg        ./strategyConfig_scalp.json \
  --start      2025-01-01 \
  --end        2025-02-01 \
  --n-workers  16 \
  --overwrite

# PRECOMPUTE INDICATORS
python precompute_features.py --config strategyConfig_scalp_1s.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-06-25 --end 2025-06-26

python precompute_features.py --config temp_config_1s.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-06-25 --end 2025-06-26


python precompute_features.py --config strategyConfig_scalp_1s_fixed.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-07-07 --end 2025-07-08


python precompute_features.py --config strategyConfig_scalp_5m_fixed.json --raw-data-dir parquet_raw --output-dir parquet_processed --start 2025-07-07 --end 2025-07-08

python create_continuous_ohlcv.py


python live_trading.py --cfg strategyConfig_scalp_1s.json --use-1s-decisions --log-level INFO